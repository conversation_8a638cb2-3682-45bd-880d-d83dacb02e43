import { ControlType, getPropertyControlsNew } from "@repo/shared/components/editor/property-controls-new";
import { addPropertyControlHandler } from "../../property-controls/propertyControlHandler";
import { Widget } from "@repo/shared/lib/types/editor";
import { Input } from "@repo/shared/components/ui/input";
import { useWidgetSettings } from "@/lib/hooks/useWidgetSettings";
import { Label } from "@repo/shared/components/ui/label";


 function TextPropertyControl(props: {widget: Widget, property: string}) {
    const { settings, updateSettings } = useWidgetSettings(props.widget)
    const propertyControls = getPropertyControlsNew(props.widget.componentName)
    const propertyControl = propertyControls?.[props.property]


    return (
        <div className="flex flex-row gap-2 items-center">

            <Label className="text-sm ">{propertyControl?.title ?? props.property}</Label>
            
            <Input type="text"  value={settings[props.property]} onChange={(e) => {
                updateSettings({[props.property] : e.target.value})
            }} />
        </div>
    )
}


addPropertyControlHandler(ControlType.Text, (props: {widget: Widget, property: string}) => <TextPropertyControl {...props}/>)

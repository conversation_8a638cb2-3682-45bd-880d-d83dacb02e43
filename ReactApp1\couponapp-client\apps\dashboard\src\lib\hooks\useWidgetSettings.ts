import { useEditor } from '@/lib/hooks/useEditor'
import { Widget } from '@repo/shared/lib/types/editor'
import { useMemo, useCallback, useRef } from 'react';
import { getPropertyControlsNew } from '@repo/shared/components/editor/property-controls-new';

// Deep equality check for objects
function deepEqual(a: any, b: any): boolean {
    if (a === b) return true;
    if (a == null || b == null) return false;
    if (typeof a !== 'object' || typeof b !== 'object') return false;

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) return false;

    for (const key of keysA) {
        if (!keysB.includes(key)) return false;
        if (!deepEqual(a[key], b[key])) return false;
    }

    return true;
}


export function getWidgetSettingsWithDefaults(widget: Widget) {
    const propertyControls = getPropertyControlsNew(widget?.componentName);

    if (!propertyControls) {
        return {
            ...widget?.settings,
        }
    }

    // Start with existing widget settings
    const result = { ...widget?.settings };

    // For each property control, check if setting is undefined and apply default
    Object.entries(propertyControls).forEach(([propertyKey, propertyControl]) => {
        if (result[propertyKey] === undefined && propertyControl.defaultValue !== undefined) {
            result[propertyKey] = propertyControl.defaultValue;
        }
    });

    return result;
}

export const useWidgetSettings = (widget: Widget, breakpoint?: string): { settings: any; updateSettings: (updates: Partial<any>) => void } => {
    const { batchUpdateWidgetSettings } = useEditor()
    const previousSettingsRef = useRef<any>(null)

    const settingsWithDefaults = useMemo(() => {
        const newSettings = getWidgetSettingsWithDefaults(widget)

        // Only return new object if settings actually changed
        if (previousSettingsRef.current && deepEqual(previousSettingsRef.current, newSettings)) {
            return previousSettingsRef.current
        }

        previousSettingsRef.current = newSettings
        return newSettings
    }, [widget?.settings, widget?.componentName])

    const updateSettings = useCallback((updates: Partial<any>) => {
        batchUpdateWidgetSettings(widget.id, (breakpoint as 'desktop' | 'mobile') ?? 'desktop', updates)
    }, [batchUpdateWidgetSettings, widget.id, breakpoint])

    return {
        settings: settingsWithDefaults,
        updateSettings,
    }
}
